import { createContext, useCallback, useEffect, useState } from "react";
import { useRequest } from "ahooks";
import { getFullLibraryData } from "@/api/fatWall";


export interface UseLibraryDataProps {
  lib_id: number;
  name: string;
  count: number;
  status: string;
  percent?: number;
}

// 获取媒体库列表hook
export const useVideoLibraryList = (props?: { effectNotLoading?: boolean, isTv?: boolean }) => {

  const { runAsync: getLibraryData } = useRequest(getFullLibraryData, { manual: true, }); // 获取所有媒体库

  // 获取媒体库列表
  const [libraries, setLibraries] = useState<UseLibraryDataProps[]>([]);
  const [myLibraries, setMyLibraries]= useState<UseLibraryDataProps[]>([]);

  const getLib = useCallback(async (noIcon?: boolean) => {
    const res = await getLibraryData(
      { sort_type: 0, asc: 0, tv: props?.isTv ? 1 : 0 },
      noIcon ? undefined : { loadingMode: 'icon' }).catch((e) => console.log('查询所有媒体库失败：', e));
    if (res && res.code === 0 && res.data) {
      const myLibs = res.data.my_libs.libs.map((item) => { return { lib_id: item.lib_id, name: item.name, count: item.media_count, status: item.scan_status, percent: item.scan_percent } });
      setMyLibraries(myLibs)
      const otherLibs = res.data.share2me.libs.map((item) => { return { lib_id: item.lib_id, name: item.name, count: item.media_count, status: item.scan_status, percent: item.scan_percent } });
      const allLibs = myLibs.concat(otherLibs);
      setLibraries(allLibs);
      return myLibs.length; // 返回我的媒体库数量
    }
    return 0; // 如果获取失败，返回0
  }, [getLibraryData, props?.isTv])

  useEffect(() => {
    getLib(props?.effectNotLoading);
  }, [getLib, props?.effectNotLoading])

  return {
    myLibraries,
    libraries,
    getLib,
    setLibraries
  }
}

// 媒体库上下文
export const LibraryContext = createContext<{
  libs: UseLibraryDataProps[],
  setLibs: React.Dispatch<React.SetStateAction<UseLibraryDataProps[]>>,
  refreshLibraries: (noIcon?: boolean) => Promise<void>
}>({ libs: [], setLibs: (v) => null, refreshLibraries: async () => { } }); // 媒体库列表