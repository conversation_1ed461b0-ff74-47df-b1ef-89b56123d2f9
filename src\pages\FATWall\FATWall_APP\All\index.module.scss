.container {
  width: 100%;
  height: calc(100% - 56px - 12px);
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  padding: 0 16px 20px 16px;

  .filter_films_container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 0 10px;
    user-select: none;
  }

  .filter_float_panel_container {
    width: 100%;
    height: 100%;
    padding: 0 12px;
    background-color: var(--modal-content-background-color);

    .filter_float_panel_navBar {
      display: flex;
      align-items: center;
      span {
        flex: 1;
        font-family: MiSans W;
        font-weight: 500;
        font-size: 20px;
        line-height: 100%;
        letter-spacing: 0px;
        text-align: center;
        color: var(--text-color);
      }
      img {
        position: absolute;
        left: 27px;
        height: 26px;
      }
    }

    .filter_float_panel_content {
      padding: 0 12px;
      background-color: var(--modal-content-background-color);

      :global {
        .adm-list-item-content,
        .adm-list-body {
          border: none !important;
        }
      }
      .filter_float_panel_content_check_list_container {
        width: 100%;
        // border-radius: 16px;
        overflow: hidden;
        background-color: var(--modal-content-background-color) !important;
        :global {
          .adm-list-item {
            background-color: var(--modal-content-background-color) !important;
            color: var(--text-color);
          }
        }
      }
      .filter_float_panel_content_list_title {
        font-family: MiSans;
        font-weight: 400;
        font-size: 14px;
        line-height: 100%;
        letter-spacing: 0px;
        color: rgba(140, 147, 176, 1);
        display: block;
        margin: 28px 0 12px 0;
      }
    }
  }
}

.right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 14px;
  margin-right: 4px;
  
  img {
    height: 26px;
  }
}

.all_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.morePopoverContainer {
  :global(.adm-popover-inner) {
    border-radius: 15px !important;
  }
}

.morePopover {
  min-width: 160px;
}

.morePopoverItem {
  padding: 10px 8px;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #000000;
  transition: background-color 0.2s;

  &:hover,
  &:active {
    background-color: rgba(255, 255, 255, 0.1);
  }

  &:first-child {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
  }

  &:last-child {
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
  }
}

.morePopoverText {
  font-size: 16px;
}

.collapse_library_container {
  overflow: hidden;
  padding: 0 16px;
  margin-bottom: 20px;

  .collapse_library_scroll_container {
    width: 100%;
    overflow-x: auto; /* 启用横向滚动 */
    overflow-y: hidden; /* 隐藏纵向滚动 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */

    .collapse_library_scroll_container_item_container {
      display: flex;
      flex-wrap: nowrap; /* 允许换行 */
      width: max-content; /* 宽度由内容撑开 */
      gap: 12px; /* item间距 */

      .collapse_library_container_content {
        width: 160px;
        height: 54px;
        padding: 0 13px;
        display: flex;
        justify-content: space-between;
        gap: 2px;
        align-items: center;

        font-family: MiSans;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        color: var(--text-color);
        background-color: var(--all-library-bg);
        border-radius: 6px;

        > span {
          width: 60px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .collapse_library_container_content_right {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          font-family: MiSans;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          color: var(--subtitle-text-color);
          flex: 1;

          .loading_container {
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            font-size: 14px;
          }
        }

        .collapse_library_container_content_right_img {
          height: 22px;
        }
      }
    }
  }
}
